#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SSL相关的日志功能
专门用于测试和验证SSL错误的日志记录
"""

import os
import sys
from config import Config

def test_ssl_logs():
    """测试SSL相关的日志功能"""
    print("测试SSL日志功能")
    print("=" * 50)
    
    # 临时设置日志级别为debug
    config = Config()
    original_log_level = config.get("log_level", "error")
    config.set("log_level", "debug")
    
    try:
        # 测试一个会产生SSL错误的URL（如果有的话）
        from novel_finder import NovelFinder
        
        print("1. 创建NovelFinder实例...")
        finder = NovelFinder()
        
        # 测试下载一个HTTPS文件（模拟SSL错误场景）
        print("\n2. 测试SSL连接...")
        
        # 创建一个模拟的小说信息，包含HTTPS下载链接
        test_novel_info = {
            'id': 'test123',
            'name_all': 'test_ssl_file.txt'
        }
        
        print("测试获取下载链接...")
        download_url = finder._get_download_url(test_novel_info)
        
        if download_url and download_url.startswith('https://'):
            print(f"获取到HTTPS下载链接: {download_url}")
            print("测试下载文件（这可能会产生SSL错误）...")
            
            # 尝试下载文件，这里可能会遇到SSL错误
            file_path = os.path.join(finder.stories_dir, "test_ssl_download.txt")
            result = finder._download_file(download_url, file_path)
            
            if result:
                print(f"下载成功: {result}")
                # 清理测试文件
                if os.path.exists(result):
                    os.remove(result)
            else:
                print("下载失败（这是预期的，用于测试SSL错误日志）")
        else:
            print("未获取到HTTPS下载链接，无法测试SSL错误")
        
        # 测试Config的网络请求
        print("\n3. 测试Config的网络请求...")
        print("测试卡密验证（可能涉及SSL）...")
        
        # 这会触发网络请求，可能遇到SSL问题
        verify_result, message = config.verify_key()
        print(f"验证结果: {verify_result}, 消息: {message}")
        
        print("\n4. 检查日志文件...")
        log_dir = "logs"
        if os.path.exists(log_dir):
            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
            if log_files:
                latest_log = max(log_files)
                log_path = os.path.join(log_dir, latest_log)
                print(f"最新日志文件: {log_path}")
                
                # 显示包含SSL相关信息的日志行
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    ssl_lines = []
                    for line in lines:
                        if any(keyword in line.lower() for keyword in ['ssl', 'https', '证书', 'certificate', 'connection']):
                            ssl_lines.append(line.strip())
                    
                    if ssl_lines:
                        print(f"找到 {len(ssl_lines)} 行SSL相关日志:")
                        print("-" * 50)
                        for line in ssl_lines[-10:]:  # 显示最后10行
                            print(line)
                    else:
                        print("未找到SSL相关的日志")
                        print("显示最后10行日志:")
                        print("-" * 50)
                        for line in lines[-10:]:
                            print(line.strip())
                            
                except Exception as e:
                    print(f"读取日志文件失败: {e}")
            else:
                print("没有找到日志文件")
        else:
            print("日志目录不存在")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始日志级别
        config.set("log_level", original_log_level)
        print(f"\n日志级别已恢复为: {original_log_level}")

def main():
    """主函数"""
    print("SSL日志测试工具")
    print("此工具专门测试SSL相关功能的日志输出")
    print("将临时设置日志级别为debug，测试完成后自动恢复")
    print()
    
    test_ssl_logs()
    
    print("\n测试完成！")
    print("如果遇到SSL错误，请查看日志文件中的详细信息")
    print("日志文件位置: logs/目录下")

if __name__ == "__main__":
    main()
