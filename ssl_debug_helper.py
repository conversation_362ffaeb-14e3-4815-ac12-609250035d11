#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL调试助手
专门用于诊断SSL连接问题，特别是针对打包后的程序
"""

import ssl
import socket
import requests
import urllib3
import sys
import os
from urllib.parse import urlparse
from config import Config
from utils import Logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def check_ssl_environment():
    """检查SSL环境信息"""
    print("=== SSL环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"SSL版本: {ssl.OPENSSL_VERSION}")
    print(f"SSL版本号: {ssl.OPENSSL_VERSION_NUMBER}")
    print(f"SSL版本信息: {ssl.OPENSSL_VERSION_INFO}")
    
    # 检查SSL模块是否可用
    try:
        context = ssl.create_default_context()
        print("✓ SSL模块可用")
        print(f"默认SSL上下文协议: {context.protocol}")
        print(f"默认SSL上下文选项: {context.options}")
        print(f"默认SSL上下文验证模式: {context.verify_mode}")
    except Exception as e:
        print(f"✗ SSL模块不可用: {e}")
    
    # 检查证书路径
    try:
        import certifi
        print(f"✓ certifi可用，证书路径: {certifi.where()}")
    except ImportError:
        print("✗ certifi不可用")
    
    # 检查requests版本
    print(f"requests版本: {requests.__version__}")
    print(f"urllib3版本: {urllib3.__version__}")
    print()

def test_basic_ssl_connection(hostname, port=443):
    """测试基本SSL连接"""
    print(f"=== 测试SSL连接到 {hostname}:{port} ===")
    
    try:
        # 创建socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 连接到主机
        print(f"连接到 {hostname}:{port}...")
        sock.connect((hostname, port))
        print("✓ TCP连接成功")
        
        # 创建SSL上下文（忽略证书验证）
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # 包装SSL
        ssl_sock = context.wrap_socket(sock, server_hostname=hostname)
        print("✓ SSL握手成功")
        
        # 获取证书信息
        cert = ssl_sock.getpeercert()
        if cert:
            print(f"证书主题: {cert.get('subject', 'N/A')}")
            print(f"证书颁发者: {cert.get('issuer', 'N/A')}")
            print(f"证书版本: {cert.get('version', 'N/A')}")
        
        ssl_sock.close()
        return True
        
    except Exception as e:
        print(f"✗ SSL连接失败: {type(e).__name__}: {e}")
        return False
    finally:
        try:
            sock.close()
        except:
            pass
    print()

def test_requests_with_ssl(url):
    """测试requests库的SSL连接"""
    print(f"=== 测试requests SSL连接到 {url} ===")
    
    # 解析URL
    parsed = urlparse(url)
    hostname = parsed.hostname
    
    try:
        # 测试1: 标准请求（验证SSL）
        print("测试1: 标准请求（验证SSL）")
        try:
            response = requests.get(url, timeout=10)
            print(f"✓ 成功 - 状态码: {response.status_code}")
        except requests.exceptions.SSLError as e:
            print(f"✗ SSL错误: {e}")
        except Exception as e:
            print(f"✗ 其他错误: {e}")
        
        # 测试2: 忽略SSL验证
        print("测试2: 忽略SSL验证")
        try:
            response = requests.get(url, verify=False, timeout=10)
            print(f"✓ 成功 - 状态码: {response.status_code}")
            return True
        except Exception as e:
            print(f"✗ 失败: {e}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    print()

def test_novel_finder_ssl():
    """测试NovelFinder的SSL连接"""
    print("=== 测试NovelFinder SSL连接 ===")
    
    # 临时设置日志级别为debug
    config = Config()
    original_log_level = config.get("log_level", "error")
    config.set("log_level", "debug")
    
    try:
        from novel_finder import NovelFinder
        
        # 创建NovelFinder实例
        finder = NovelFinder()
        
        # 测试搜索功能
        print("测试搜索功能...")
        results = finder.search("测试")
        
        if results:
            print(f"✓ 搜索成功，找到 {len(results)} 个结果")
            
            # 测试获取下载链接
            print("测试获取下载链接...")
            first_novel = results[0]
            download_url = finder._get_download_url(first_novel)
            
            if download_url:
                print(f"✓ 获取下载链接成功: {download_url}")
                
                # 解析下载链接的主机
                parsed = urlparse(download_url)
                if parsed.hostname:
                    # 测试下载主机的SSL连接
                    print(f"测试下载主机SSL连接: {parsed.hostname}")
                    test_basic_ssl_connection(parsed.hostname)
                    test_requests_with_ssl(download_url)
                
            else:
                print("✗ 获取下载链接失败")
        else:
            print("✗ 搜索失败或无结果")
            
    except Exception as e:
        print(f"✗ 测试NovelFinder失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始日志级别
        config.set("log_level", original_log_level)
    print()

def main():
    """主函数"""
    print("SSL调试助手")
    print("=" * 60)
    print("此工具将帮助诊断SSL连接问题")
    print()
    
    # 检查SSL环境
    check_ssl_environment()
    
    # 测试常见的SSL连接
    test_hosts = [
        "www.google.com",
        "www.baidu.com", 
        "qiuov.cn",
        "pdf1.webgetstore.com"
    ]
    
    for host in test_hosts:
        test_basic_ssl_connection(host)
    
    # 测试具体的URL
    test_urls = [
        "https://www.google.com",
        "https://www.baidu.com",
        "http://qiuov.cn:3339/api/time.php"
    ]
    
    for url in test_urls:
        test_requests_with_ssl(url)
    
    # 测试NovelFinder
    test_novel_finder_ssl()
    
    print("=== 诊断完成 ===")
    print("请检查logs目录下的日志文件获取更多详细信息")

if __name__ == "__main__":
    main()
