#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试NovelFinder的日志功能
用于验证日志输出是否正常工作
"""

import os
import sys
from novel_finder import NovelFinder
from config import Config

def test_novel_finder_logs():
    """测试NovelFinder的日志功能"""
    print("测试NovelFinder日志功能")
    print("=" * 50)
    
    # 临时设置日志级别为debug
    config = Config()
    original_log_level = config.get("log_level", "error")
    config.set("log_level", "debug")
    
    try:
        # 创建NovelFinder实例
        print("1. 创建NovelFinder实例...")
        finder = NovelFinder()
        
        # 测试搜索功能（使用一个简单的关键词）
        print("\n2. 测试搜索功能...")
        results = finder.search("测试")
        print(f"搜索结果数量: {len(results)}")
        
        # 测试文件名清理功能
        print("\n3. 测试文件名清理功能...")
        test_filename = "测试<文件>名称:包含|非法*字符?.txt"
        cleaned_filename = finder._sanitize_filename(test_filename)
        print(f"原始文件名: {test_filename}")
        print(f"清理后文件名: {cleaned_filename}")
        
        # 测试获取小说路径功能
        print("\n4. 测试获取小说路径功能...")
        path = finder.get_novel_path("不存在的文件.txt")
        print(f"查找不存在文件的结果: {path}")
        
        # 如果有搜索结果，测试下载功能（但不实际下载）
        if results:
            print("\n5. 测试下载链接获取功能...")
            first_novel = results[0]
            print(f"测试小说信息: {first_novel}")
            
            # 只测试获取下载链接，不实际下载
            download_url = finder._get_download_url(first_novel)
            if download_url:
                print(f"获取到下载链接: {download_url}")
                print("注意：为了测试目的，不会实际下载文件")
            else:
                print("获取下载链接失败")
        
        print("\n6. 检查日志文件...")
        log_dir = "logs"
        if os.path.exists(log_dir):
            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
            if log_files:
                latest_log = max(log_files)
                log_path = os.path.join(log_dir, latest_log)
                print(f"最新日志文件: {log_path}")
                
                # 显示最后几行日志
                try:
                    with open(log_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"日志文件总行数: {len(lines)}")
                        print("最后10行日志内容:")
                        print("-" * 50)
                        for line in lines[-10:]:
                            print(line.strip())
                except Exception as e:
                    print(f"读取日志文件失败: {e}")
            else:
                print("没有找到日志文件")
        else:
            print("日志目录不存在")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 恢复原始日志级别
        config.set("log_level", original_log_level)
        print(f"\n日志级别已恢复为: {original_log_level}")

def main():
    """主函数"""
    print("NovelFinder日志测试工具")
    print("此工具将临时设置日志级别为debug，测试各种功能的日志输出")
    print("测试完成后会自动恢复原始日志级别")
    print()
    
    # 询问用户是否继续
    response = input("是否继续测试？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("测试已取消")
        return
    
    test_novel_finder_logs()
    
    print("\n测试完成！")
    print("请检查logs目录下的日志文件，查看详细的debug信息")

if __name__ == "__main__":
    main()
