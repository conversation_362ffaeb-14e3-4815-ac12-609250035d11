import os
import json
import datetime
import requests
import urllib3
from utils import Logger

class Config:
    """配置管理类，负责保存和加载配置信息，使用单例模式确保在整个应用中只有一个实例"""

    _instance = None

    def __new__(cls, *args, **kwargs):
        """创建单例实例"""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, config_file="config.json"):
        """初始化配置管理器

        Args:
            config_file (str): 配置文件路径
        """
        # 确保只初始化一次
        if self._initialized:
            return

        self.config_file = config_file
        self.config = self._load_config()
        self.server_base_url = "http://qiuov.cn:3339"  # 服务器基础地址

        # 禁用SSL警告（因为我们使用verify=False）
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 仅在内存中保存的数据
        self._permissions = []  # 权限列表保存在内存中，不写入配置文件

        # 延迟初始化日志器，避免循环导入
        self._logger = None

        self._initialized = True

    def _get_logger(self):
        """获取日志器实例，延迟初始化避免循环导入"""
        if self._logger is None:
            # 直接从配置中获取日志级别，避免循环依赖
            log_level_str = self.config.get("log_level", "error")
            from utils import LogLevel, Logger
            log_level = LogLevel.from_string(log_level_str)
            self._logger = Logger("Config", log_level)
            self._logger.debug("Config日志器初始化完成")
            self._logger.debug(f"配置文件: {self.config_file}")
            self._logger.debug(f"服务器地址: {self.server_base_url}")
        return self._logger

    @property
    def logger(self):
        """日志器属性"""
        return self._get_logger()

    def _load_config(self):
        """加载配置

        Returns:
            dict: 配置信息
        """
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print("使用默认配置")
        else:
            print("配置文件不存在，创建默认配置")

        # 返回默认配置
        default_config = {
            "log_level": "error",
            "last_groups": [],
            "owner_id": "",
            "novel_search_enabled": True,
            "link_parser_enabled": True,
            "shuqi_enabled": True,
            "zhangxinlei_enabled": True,
            "lofter_enabled": True,
            "key": ""
        }

        # 首次创建配置时，自动保存到文件
        if not os.path.exists(self.config_file):
            self.config = default_config
            # 遍历配置项并保存
            for key, value in default_config.items():
                self.set(key, value)

        return default_config

    def save_config(self):
        """保存所有配置项到文件"""
        # 遍历所有配置项并保存
        for key, value in self.config.items():
            self.set(key, value)


    def get(self, key, default=None):
        """获取配置项

        Args:
            key (str): 配置项键名
            default: 默认值

        Returns:
            配置项值
        """
        return self.config.get(key, default)

    def set(self, key, value):
        """设置配置项

        Args:
            key (str): 配置项键名
            value: 配置项值
        """
        # 先获取完整的配置
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    current_config = json.load(f)
            except:
                # 如果读取失败，使用当前内存中的配置
                current_config = self.config.copy()
        else:
            current_config = self.config.copy()

        # 只更新指定的键值
        current_config[key] = value

        # 更新内存中的配置
        self.config = current_config

        # 保存到文件
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)

    def save_last_groups(self, groups):
        """保存上次选择的群聊

        Args:
            groups (list): 群聊列表
        """
        self.set("last_groups", groups)

    def get_last_groups(self):
        """获取上次选择的群聊

        Returns:
            list: 群聊列表
        """
        return self.get("last_groups", [])

    def set_key(self, key):
        """设置卡密

        Args:
            key (str): 卡密
        """
        self.set("key", key)

    def get_key(self):
        """获取卡密

        Returns:
            str: 卡密
        """
        return self.get("key", "")

    def verify_key(self):
        """验证卡密

        Returns:
            bool: 验证结果
            str: 错误信息或到期时间
        """
        key = self.get_key()
        if not key:
            self.logger.debug("卡密为空，验证失败")
            return False, "未设置卡密"

        self.logger.debug("开始验证卡密")
        self.logger.debug(f"卡密: {key[:8]}...")  # 只显示前8位，保护隐私

        # 获取服务器时间
        try:
            time_url = f"{self.server_base_url}/api/time.php"
            self.logger.debug(f"获取服务器时间，URL: {time_url}")

            server_time_resp = requests.get(time_url, verify=False, timeout=30)

            self.logger.debug(f"服务器时间响应状态码: {server_time_resp.status_code}")
            self.logger.debug(f"服务器时间响应头: {dict(server_time_resp.headers)}")

            if server_time_resp.status_code != 200:
                self.logger.debug(f"获取服务器时间失败，HTTP状态码: {server_time_resp.status_code}")
                return False, "获取服务器时间失败"

            server_time_data = server_time_resp.json()
            self.logger.debug(f"服务器时间响应数据: {server_time_data}")

            server_time = server_time_data.get("data")
            if not server_time:
                self.logger.debug("服务器时间响应数据中没有时间信息")
                return False, "获取服务器时间失败"

            self.logger.debug(f"获取到服务器时间: {server_time}")

        except requests.exceptions.SSLError as e:
            self.logger.debug(f"获取服务器时间SSL错误: {type(e).__name__}: {e}")
            print(f"获取服务器时间异常: {e}")
            return False, "获取服务器时间异常"
        except Exception as e:
            self.logger.debug(f"获取服务器时间异常: {type(e).__name__}: {e}")
            print(f"获取服务器时间异常: {e}")
            return False, "获取服务器时间异常"

        # 验证卡密
        try:
            login_url = f"{self.server_base_url}/api/login.php"
            login_data = {"key": key}

            self.logger.debug(f"验证卡密，URL: {login_url}")
            self.logger.debug(f"验证卡密请求数据: {{'key': '{key[:8]}...'}}")

            resp = requests.post(login_url, data=login_data, verify=False, timeout=30)

            self.logger.debug(f"验证卡密响应状态码: {resp.status_code}")
            self.logger.debug(f"验证卡密响应头: {dict(resp.headers)}")

            if resp.status_code != 200:
                self.logger.debug(f"验证卡密失败，HTTP状态码: {resp.status_code}")
                return False, "验证卡密失败"

            # 使用utf-8-sig解码以处理可能的BOM
            try:
                data = json.loads(resp.content.decode('utf-8-sig'))
                self.logger.debug("使用utf-8-sig解码响应成功")
            except:
                data = resp.json()  # 如果上面的方法失败，尝试默认方法
                self.logger.debug("使用默认方法解码响应")

            self.logger.debug(f"验证卡密响应数据: {data}")

            if data.get("code") != 200:
                error_msg = data.get("msg", "验证卡密失败")
                self.logger.debug(f"验证卡密失败，错误代码: {data.get('code')}, 错误信息: {error_msg}")
                return False, error_msg

            response_data = data.get("data", {})
            expire_time = response_data.get("expires_at", "")
            permissions = response_data.get("permissions", [])

            self.logger.debug(f"卡密到期时间: {expire_time}")
            self.logger.debug(f"卡密权限: {permissions}")

            # 保存权限信息到配置
            self._permissions = permissions

            # 检查是否过期
            expire_date = datetime.datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
            server_date = datetime.datetime.strptime(server_time, "%Y-%m-%d %H:%M:%S")

            self.logger.debug(f"到期日期: {expire_date}")
            self.logger.debug(f"服务器日期: {server_date}")

            if expire_date <= server_date:
                self.logger.debug("卡密已过期")
                return False, "卡密已过期"

            self.logger.debug("卡密验证成功")
            return True, expire_time

        except requests.exceptions.SSLError as e:
            self.logger.debug(f"验证卡密SSL错误: {type(e).__name__}: {e}")
            return False, "验证卡密异常"
        except Exception as e:
            self.logger.debug(f"验证卡密异常: {type(e).__name__}: {e}")
            return False, "验证卡密异常"

    def get_permissions(self):
        """获取当前卡密权限

        Returns:
            list: 权限列表，如 ["找小说", "链接解析"]
        """
        return self._permissions