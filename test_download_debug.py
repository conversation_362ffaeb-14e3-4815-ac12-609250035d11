#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载调试测试脚本
用于测试和诊断SSL下载问题
"""

import sys
import os
from novel_finder import NovelFinder
from utils import Logger, LogLevel

def test_download_with_debug():
    """使用调试模式测试下载功能"""
    
    # 设置调试级别的日志器
    logger = Logger("DownloadTest", LogLevel.DEBUG)
    
    print("=" * 60)
    print("下载调试测试")
    print("=" * 60)
    
    # 创建NovelFinder实例
    novel_finder = NovelFinder()
    
    # 模拟一个小说信息对象（使用你遇到问题的URL）
    test_novel_info = {
        "id": "test_novel_id",
        "name_all": "测试小说.txt"
    }
    
    # 测试URL（你遇到问题的URL）
    test_url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    
    print(f"测试URL: {test_url}")
    print("-" * 60)
    
    # 直接测试下载文件方法
    test_file_path = os.path.join("stories", "test_download.txt")
    
    # 确保stories目录存在
    if not os.path.exists("stories"):
        os.makedirs("stories")
    
    print("开始测试下载...")
    result = novel_finder._download_file(test_url, test_file_path)
    
    if result:
        print(f"✓ 下载成功: {result}")
        # 检查文件是否存在
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"文件大小: {file_size} 字节")
            
            # 读取文件前几行内容
            try:
                with open(result, 'r', encoding='utf-8') as f:
                    content = f.read(500)  # 读取前500个字符
                print("文件内容预览:")
                print("-" * 30)
                print(content)
                print("-" * 30)
            except Exception as e:
                print(f"读取文件内容失败: {e}")
        else:
            print("✗ 文件不存在")
    else:
        print("✗ 下载失败")
    
    print("=" * 60)
    print("测试完成")

def test_ssl_diagnosis():
    """测试SSL诊断功能"""
    print("=" * 60)
    print("SSL诊断测试")
    print("=" * 60)
    
    novel_finder = NovelFinder()
    test_url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    
    novel_finder._diagnose_ssl_connection(test_url)
    
    print("=" * 60)
    print("SSL诊断完成")

def main():
    """主函数"""
    print("下载调试工具")
    print("1. 测试下载功能（带详细日志）")
    print("2. 测试SSL诊断功能")
    print("3. 同时运行两个测试")
    
    choice = input("请选择测试类型 (1/2/3): ").strip()
    
    if choice == "1":
        test_download_with_debug()
    elif choice == "2":
        test_ssl_diagnosis()
    elif choice == "3":
        test_download_with_debug()
        print("\n")
        test_ssl_diagnosis()
    else:
        print("无效选择")
        return
    
    # 询问是否查看日志文件
    view_logs = input("\n是否查看详细日志文件? (y/n): ").strip().lower()
    if view_logs == 'y':
        import datetime
        log_file = f"logs/{datetime.datetime.now().strftime('%Y-%m-%d')}.log"
        if os.path.exists(log_file):
            print(f"\n日志文件内容 ({log_file}):")
            print("=" * 60)
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    print(f.read())
            except Exception as e:
                print(f"读取日志文件失败: {e}")
        else:
            print(f"日志文件不存在: {log_file}")

if __name__ == "__main__":
    main()
