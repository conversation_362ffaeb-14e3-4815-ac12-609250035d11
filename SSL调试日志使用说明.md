# SSL调试日志使用说明

## 概述

为了帮助排查SSL证书验证失败的问题，我已经在代码中添加了详细的debug级别日志输出。这些日志将帮助您诊断网络连接、SSL握手、证书验证等各个环节的问题。

## 日志配置

### 1. 日志级别设置

程序的日志级别在 `config.json` 文件中配置：

```json
{
    "log_level": "debug"  // 设置为debug可以看到详细日志
}
```

**重要说明：**
- 开发调试时设置为 `"debug"` 
- 正式打包后设置为 `"error"`，避免用户看到过多日志信息

### 2. 日志文件位置

日志文件保存在 `logs/` 目录下，文件名格式为 `YYYY-MM-DD.log`，例如：
- `logs/2025-01-20.log`

## 添加的日志功能

### 1. NovelFinder类日志

在 `novel_finder.py` 中添加了以下日志：

#### 初始化日志
- NovelFinder初始化完成
- 小说存储目录创建
- SSL警告禁用状态

#### 搜索功能日志
- 搜索关键词和请求参数
- API响应状态码和响应头
- 搜索结果数量

#### 下载功能日志
- 下载URL解析（主机、协议、端口）
- 请求配置参数
- 下载响应状态码和响应头
- 文件大小信息
- 下载进度和完成状态
- **详细的SSL错误信息**

#### 文件处理日志
- 文件名清理过程
- 编码转换尝试
- 临时文件处理

### 2. Config类日志

在 `config.py` 中添加了以下日志：

#### 卡密验证日志
- 服务器时间获取过程
- 卡密验证请求和响应
- **SSL错误详细信息**
- 权限和到期时间信息

## 使用方法

### 1. 临时启用debug日志

如果您想临时查看详细日志，可以：

1. 修改 `config.json` 文件：
   ```json
   {
       "log_level": "debug"
   }
   ```

2. 运行程序，重现SSL错误

3. 查看 `logs/` 目录下的日志文件

4. 完成调试后，将日志级别改回 `"error"`

### 2. 使用测试脚本

我提供了两个测试脚本：

#### test_novel_finder_logs.py
```bash
python test_novel_finder_logs.py
```
- 测试NovelFinder的各种功能
- 自动设置debug日志级别
- 显示最新的日志内容

#### ssl_debug_helper.py
```bash
python ssl_debug_helper.py
```
- 全面的SSL环境检查
- 测试各种SSL连接
- 专门诊断SSL问题

## 日志分析

### 查找SSL错误

在日志文件中搜索以下关键词：

1. **SSL错误**：
   ```
   SSL错误详细信息
   SSLError
   certificate verify failed
   ```

2. **连接错误**：
   ```
   连接错误详细信息
   ConnectionError
   Max retries exceeded
   ```

3. **超时错误**：
   ```
   超时错误详细信息
   Timeout
   ```

### 常见SSL错误类型

1. **证书验证失败**：
   ```
   [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate
   ```
   - 原因：缺少根证书或证书链不完整
   - 解决：程序已设置 `verify=False` 忽略证书验证

2. **SSL模块不可用**：
   ```
   SSLError: SSL module is not available
   ```
   - 原因：打包时SSL模块未正确包含
   - 解决：检查打包配置，确保SSL相关模块被包含

3. **协议版本不支持**：
   ```
   SSLError: [SSL: UNSUPPORTED_PROTOCOL] unsupported protocol
   ```
   - 原因：SSL/TLS协议版本不匹配
   - 解决：更新SSL配置或使用兼容的协议版本

## 打包注意事项

### 1. 确保SSL模块包含

在 `main.spec` 文件中确保包含：
```python
hiddenimports=[
    'ssl',
    'socket',
    'requests.adapters',
    'requests.packages.urllib3',
    'urllib3.util.retry',
    'urllib3.util.connection',
    # ... 其他模块
]
```

### 2. 证书文件处理

如果需要包含证书文件：
```python
datas=[
    ('path/to/certificates', 'certificates'),
]
```

### 3. 日志级别设置

打包前确保：
```json
{
    "log_level": "error"
}
```

## 故障排除步骤

1. **启用debug日志**
2. **重现问题**
3. **查看日志文件**
4. **分析错误信息**
5. **根据错误类型采取相应措施**

## 联系支持

如果日志显示的错误信息无法解决，请提供：
1. 完整的错误日志
2. 操作系统版本
3. Python版本
4. 是否为打包后的程序

这些详细的日志信息将大大提高问题诊断的效率。
