#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
带日志的下载测试脚本
用于测试新增的日志功能，帮助排查SSL下载问题
"""

import os
import sys
from novel_finder import NovelFinder
from utils import Logger, LogLevel

def test_download_with_detailed_logs():
    """测试带详细日志的下载功能"""
    
    print("=" * 80)
    print("带日志的下载测试")
    print("=" * 80)
    
    # 创建一个DEBUG级别的日志器，确保能看到所有日志
    logger = Logger("TestDownload", LogLevel.DEBUG)
    logger.info("开始下载测试...")
    
    # 创建NovelFinder实例
    novel_finder = NovelFinder()
    
    # 模拟一个小说信息对象（使用你遇到问题的URL）
    test_novel_info = {
        "id": "test_novel_id",
        "name_all": "[废文 完结]末日亚种（np）作者鸣以和鸾.txt",
        "icon": "txt",
        "size": "未知"
    }
    
    # 直接测试下载文件方法，使用你遇到问题的URL
    test_url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    test_file_path = os.path.join("stories", "test_download_with_logs.txt")
    
    # 确保stories目录存在
    if not os.path.exists("stories"):
        os.makedirs("stories")
        logger.info("创建stories目录")
    
    print(f"测试URL: {test_url}")
    print(f"目标文件: {test_file_path}")
    print("-" * 80)
    
    # 开始测试下载
    logger.info("开始测试下载...")
    result = novel_finder._download_file(test_url, test_file_path)
    
    print("-" * 80)
    if result:
        print(f"✓ 下载成功: {result}")
        
        # 检查文件是否存在
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"文件大小: {file_size} 字节")
            
            # 读取文件前几行内容
            try:
                with open(result, 'r', encoding='utf-8') as f:
                    content = f.read(500)  # 读取前500个字符
                print("文件内容预览:")
                print("-" * 30)
                print(content)
                print("-" * 30)
            except Exception as e:
                print(f"读取文件内容失败: {e}")
        else:
            print("✗ 文件不存在")
    else:
        print("✗ 下载失败")
    
    print("=" * 80)
    print("测试完成")
    
    # 显示日志文件位置
    print(f"详细日志已保存到: logs/{logger.log_file}")
    print("请查看日志文件获取详细的调试信息")

def test_ssl_diagnosis_only():
    """仅测试SSL诊断功能"""
    print("=" * 80)
    print("SSL诊断测试")
    print("=" * 80)
    
    novel_finder = NovelFinder()
    test_url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    
    print(f"诊断URL: {test_url}")
    print("-" * 80)
    
    ssl_ok = novel_finder._diagnose_ssl_connection(test_url)
    
    print("-" * 80)
    if ssl_ok:
        print("✓ SSL连接诊断通过")
    else:
        print("✗ SSL连接诊断发现问题")
    
    print("=" * 80)
    print("SSL诊断完成")

def show_system_info():
    """显示系统信息"""
    print("=" * 80)
    print("系统信息")
    print("=" * 80)
    
    import ssl
    import requests
    import urllib3
    
    print(f"Python版本: {sys.version}")
    print(f"OpenSSL版本: {ssl.OPENSSL_VERSION}")
    print(f"SSL模块位置: {ssl.__file__}")
    
    try:
        print(f"Requests版本: {requests.__version__}")
        print(f"urllib3版本: {urllib3.__version__}")
    except:
        pass
    
    # 检查SSL证书验证设置
    print(f"SSL默认验证模式: {ssl.create_default_context().verify_mode}")
    print(f"SSL默认检查主机名: {ssl.create_default_context().check_hostname}")
    
    print("=" * 80)

if __name__ == "__main__":
    # 显示系统信息
    show_system_info()
    
    print("\n请选择测试类型:")
    print("1. 完整下载测试（包含详细日志）")
    print("2. 仅SSL诊断测试")
    print("3. 退出")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        test_download_with_detailed_logs()
    elif choice == "2":
        test_ssl_diagnosis_only()
    elif choice == "3":
        print("退出测试")
    else:
        print("无效选择，执行完整下载测试...")
        test_download_with_detailed_logs()
