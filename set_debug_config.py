#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置调试配置脚本
用于临时设置日志级别为debug，以便查看详细的调试信息
"""

from config import Config

def set_debug_level():
    """设置日志级别为debug"""
    config = Config()
    
    # 获取当前日志级别
    current_level = config.get("log_level", "error")
    print(f"当前日志级别: {current_level}")
    
    # 设置为debug级别
    config.set("log_level", "debug")
    print("已设置日志级别为: debug")
    
    # 验证设置
    new_level = config.get("log_level")
    print(f"新的日志级别: {new_level}")
    
    print("\n现在可以运行程序查看详细的调试信息了")
    print("测试完成后，可以运行此脚本并选择恢复来重置日志级别")

def reset_log_level():
    """重置日志级别为error"""
    config = Config()
    
    # 获取当前日志级别
    current_level = config.get("log_level", "error")
    print(f"当前日志级别: {current_level}")
    
    # 重置为error级别
    config.set("log_level", "error")
    print("已重置日志级别为: error")
    
    # 验证设置
    new_level = config.get("log_level")
    print(f"新的日志级别: {new_level}")

def main():
    """主函数"""
    print("日志级别配置工具")
    print("1. 设置为debug级别（查看详细调试信息）")
    print("2. 重置为error级别（只显示错误信息）")
    
    choice = input("请选择操作 (1/2): ").strip()
    
    if choice == "1":
        set_debug_level()
    elif choice == "2":
        reset_log_level()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
