#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速SSL连接测试脚本
专门用于诊断SSL证书验证失败问题
"""

import requests
import ssl
import socket
import urllib3
import sys
from urllib.parse import urlparse

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_problematic_url():
    """测试有问题的URL"""
    
    # 你遇到问题的URL
    url = "https://pdf1.webgetstore.com/2025/05/20/2cfa21319e852b9af6a0bbb31867282e.txt?sg=91342312df04100831a2096404a0b3be&e=68371f95&fileName=%5B%E5%BA%9F%E6%96%87%20%E5%AE%8C%E7%BB%93%5D%E6%9C%AB%E6%97%A5%E4%BA%9A%E7%A7%8D%EF%BC%88np%EF%BC%89%E4%BD%9C%E8%80%85%E9%B8%A3%E4%BB%A5%E5%92%8C%E9%B8%BE.txt"
    
    print("SSL连接测试工具")
    print("=" * 80)
    print(f"测试URL: {url}")
    print("=" * 80)
    
    # 解析URL
    parsed_url = urlparse(url)
    host = parsed_url.hostname
    port = parsed_url.port or 443
    
    print(f"主机: {host}")
    print(f"端口: {port}")
    print("-" * 80)
    
    # 显示系统信息
    print("系统信息:")
    print(f"Python版本: {sys.version}")
    print(f"OpenSSL版本: {ssl.OPENSSL_VERSION}")
    try:
        print(f"Requests版本: {requests.__version__}")
        print(f"urllib3版本: {urllib3.__version__}")
    except:
        pass
    print("-" * 80)
    
    # 测试1: 基本TCP连接
    print("测试1: 基本TCP连接")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        sock.close()
        print("✓ TCP连接成功")
    except Exception as e:
        print(f"✗ TCP连接失败: {e}")
        return
    print()
    
    # 测试2: SSL连接（忽略证书验证）
    print("测试2: SSL连接（忽略证书验证）")
    try:
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        ssl_sock = context.wrap_socket(sock, server_hostname=host)
        ssl_sock.connect((host, port))
        
        # 获取证书信息
        cert = ssl_sock.getpeercert()
        print("✓ SSL连接成功（忽略证书验证）")
        if cert:
            print(f"证书主题: {cert.get('subject', 'N/A')}")
            print(f"证书颁发者: {cert.get('issuer', 'N/A')}")
        
        ssl_sock.close()
    except Exception as e:
        print(f"✗ SSL连接失败: {e}")
    print()
    
    # 测试3: requests库（忽略SSL验证）
    print("测试3: requests库（忽略SSL验证）")
    try:
        response = requests.head(url, verify=False, timeout=10)
        print(f"✓ requests HEAD请求成功，状态码: {response.status_code}")
        print(f"服务器: {response.headers.get('Server', 'N/A')}")
        print(f"内容类型: {response.headers.get('Content-Type', 'N/A')}")
        print(f"内容长度: {response.headers.get('Content-Length', 'N/A')}")
    except Exception as e:
        print(f"✗ requests HEAD请求失败: {e}")
    print()
    
    # 测试4: requests库完整GET请求（忽略SSL验证）
    print("测试4: requests库完整GET请求（忽略SSL验证）")
    try:
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        response = requests.get(url, verify=False, timeout=30, headers=headers, stream=True)
        print(f"✓ requests GET请求成功，状态码: {response.status_code}")
        
        # 读取前1024字节内容
        content = b""
        for chunk in response.iter_content(chunk_size=1024):
            content += chunk
            break  # 只读取第一个chunk
        
        print(f"内容长度: {len(content)} 字节")
        print(f"内容类型: {response.headers.get('Content-Type', 'N/A')}")
        
        # 尝试解码内容
        try:
            decoded_content = content.decode('utf-8')[:200]
            print(f"内容预览: {decoded_content}...")
        except:
            print("内容无法解码为UTF-8")
        
        response.close()
        
    except Exception as e:
        print(f"✗ requests GET请求失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 如果是SSL错误，提供更多信息
        if "SSL" in str(e) or "certificate" in str(e).lower():
            print("\nSSL错误详细信息:")
            print("- 这通常是由于SSL证书验证失败导致的")
            print("- 可能的原因:")
            print("  1. 服务器证书过期或无效")
            print("  2. 证书链不完整")
            print("  3. 系统时间不正确")
            print("  4. CA证书库过期")
            print("  5. 网络代理或防火墙干扰")
            
            print("\n建议解决方案:")
            print("1. 检查系统时间是否正确")
            print("2. 更新系统的CA证书库")
            print("3. 检查网络代理设置")
            print("4. 尝试使用不同的网络环境")
            print("5. 如果是企业网络，联系网络管理员")
    
    print()
    print("=" * 80)
    print("测试完成")

if __name__ == "__main__":
    test_problematic_url()
