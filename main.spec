# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    # 确保必要的模块被包含
    hiddenimports=[
        'requests.adapters',
        'requests.packages.urllib3',
        'requests.packages.urllib3.util.retry',
        'requests.packages.urllib3.util.connection',
        'urllib3.util.retry',
        'urllib3.util.connection',
        'wxautox',
        'enum',
        'functools',
        'collections',
        'collections.abc',
        'typing',
        'json.decoder',
        'json.encoder',
        'threading',
        '_thread',
        'hashlib',
        're',
        'sre_compile',
        'sre_parse',
        'sre_constants',
        'ssl',
        'socket',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    # 只排除明确不需要的大型模块
    excludes=[
        # 排除大型科学计算库
        'torch', 'tensorflow', 'numpy', 'scipy', 'pandas', 'sklearn',
        'cv2', 'opencv', 'sympy', 'networkx', 'keras', 'h5py',
        # 排除GUI相关（保留PIL，因为wxautox需要）
        'tkinter', 'turtle', 'matplotlib', 'idlelib',
        'tcl', 'tk', '_tkinter',
        # 排除测试相关
        'unittest', 'test', 'tests', 'pytest', 'nose', 'doctest',
        # 排除开发工具
        'pdb', 'profile', 'pstats', 'trace', 'pydoc', 'pygments',
        # 排除IPython/Jupyter相关
        'IPython', 'jupyter', 'notebook', 'ipykernel',
        # 排除Web框架
        'django', 'flask', 'fastapi', 'tornado',
        # 排除爬虫相关
        'selenium', 'beautifulsoup4', 'lxml', 'scrapy',
        # 排除数据库驱动
        'psycopg2', 'pymongo', 'sqlalchemy', 'mysql',
        # 排除编译器相关
        'nuitka',
        # 排除字体工具
        'fontTools',
    ],
    noarchive=False,
    optimize=1,  # 中等级别的Python字节码优化
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=True,  # 启用符号表剥离以减小文件大小
    upx=False,   # 根据用户要求不使用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['favicon.ico'],
)
